import useDataService from "@/services/useDataService";
import { GET_SKU_LIST_URL } from "@/constants/urls";
import { usePageQuery } from "../useRouteQuery";
import { useDebugQuery } from "../useDebugQuery";
import { useAuthStore } from "../../store/authStore";

export interface SKU {
  sku_id: number;
  sku_code: string;
}

interface GetSKUListResponse {
  success: boolean;
  message: string;
  code: number;
  result: {
    data: SKU[];
  };
}

export const useGetSKUList = () => {
  const { user } = useAuthStore();
  const queryKey = ["sku-list"];

  // Debug logging - remove this in production
  useDebugQuery('useGetSKUList', queryKey, true);

  return usePageQuery<GetSKUListResponse>(
    queryKey,
    async () => {
      console.log(`🚀 SKU List API call triggered for user: ${user?.user_id} (${user?.email})`);
      console.log(`📡 SKU List API endpoint: ${GET_SKU_LIST_URL}`);
      return await useDataService.getService(GET_SKU_LIST_URL);
    },
    {
      staleTime: 5 * 60 * 1000, // Cache for 5 minutes instead of infinity
    }
  );
};
