import { useMutation } from '@tanstack/react-query';
import useDataService from '../../services/useDataService';
import { GET_TENANT_URL, UPDATE_TENANT_URL, GET_COMPANY_TYPES_URL } from '../../constants/urls';
import { usePageQuery } from '../useRouteQuery';
import { useDebugQuery } from '../useDebugQuery';
import { useAuthStore } from '../../store/authStore';

interface CompanyDetails {
    tenant_name: string;
    website_url?: string;
    number_of_locations: number;
    tax_id?: string;
    street_address?: string;
    city?: string;
    state?: string;
    postal_code?: string;
    country?: string;
    [key: string]: any;
}

export const useGetCompanyDetails = (tenantId: string, enabled = true) => {
    const { user } = useAuthStore();
    const queryKey = ['company-details', tenantId];

    // Debug logging - remove this in production
    useDebugQuery('useGetCompanyDetails', queryKey, !!tenantId && enabled);

    return usePageQuery<CompanyDetails>(
        queryKey,
        async () => {
            console.log(`🚀 Company Details API call triggered for user: ${user?.user_id} (${user?.email})`);
            const endpoint = `${GET_TENANT_URL}/${tenantId}`;
            console.log(`📡 Company Details API endpoint: ${endpoint}`);
            return await useDataService.getService(endpoint);
        },
        {
            enabled: !!tenantId && enabled,
            staleTime: 5 * 60 * 1000, // Cache for 5 minutes instead of infinity
        }
    );
};

export const useUpdateCompanyDetails = () => {
    return useMutation<CompanyDetails, Error, { tenantId: string; data: CompanyDetails }>({
        mutationFn: async ({ tenantId, data }) => {
            const endpoint = `${UPDATE_TENANT_URL}/${tenantId}`;
            return await useDataService.patchService(endpoint, data);
        },
    });
};

export interface CompanyType {
    industry_id: number;
    industry_name: string;
    description: string;
    is_active: boolean;
    created_at: string;
}

export const useGetCompanyTypes = (enabled = true) => {
    const { user } = useAuthStore();
    const queryKey = ['company-types'];

    // Debug logging - remove this in production
    useDebugQuery('useGetCompanyTypes', queryKey, enabled);

    return usePageQuery<CompanyType[]>(
        queryKey,
        async () => {
            console.log(`🚀 Company Types API call triggered for user: ${user?.user_id} (${user?.email})`);
            console.log(`📡 Company Types API endpoint: ${GET_COMPANY_TYPES_URL}`);
            const response = await useDataService.getService(GET_COMPANY_TYPES_URL);
            return response?.result?.data || [];
        },
        {
            enabled,
            staleTime: 5 * 60 * 1000, // Cache for 5 minutes instead of infinity
        }
    );
};