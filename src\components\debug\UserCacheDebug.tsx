import { useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '../../store/authStore';
import { useAuthActions } from '../../hooks/useAuthActions';

/**
 * Debug component to help verify cross-user cache isolation
 * Add this temporarily to any page to see cache state
 * Remove in production
 */
export function UserCacheDebug() {
  const queryClient = useQueryClient();
  const { user } = useAuthStore();
  const { logout } = useAuthActions();

  const getAllQueries = () => {
    return queryClient.getQueryCache().getAll();
  };

  const getUserQueries = () => {
    return getAllQueries().filter(query =>
      query.queryKey.includes('user') &&
      query.queryKey.includes(user?.user_id || '')
    );
  };

  const clearUserCache = () => {
    if (user?.user_id) {
      queryClient.removeQueries({
        predicate: (query: any) => {
          return query.queryKey.includes('user') && query.queryKey.includes(user.user_id);
        }
      });
      console.log(`🧹 Manually cleared cache for user: ${user.user_id}`);
    }
  };

  if (!user) {
    return (
      <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <strong>Debug:</strong> No user logged in
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded max-w-md">
      <div className="text-sm">
        <strong>🐛 Cache Debug</strong>
        <div className="mt-2">
          <div><strong>User:</strong> {user.user_id}</div>
          <div><strong>Email:</strong> {user.email}</div>
          <div><strong>Total Queries:</strong> {getAllQueries().length}</div>
          <div><strong>User Queries:</strong> {getUserQueries().length}</div>
        </div>

        <div className="mt-3 space-y-2">
          <button
            onClick={clearUserCache}
            className="bg-yellow-500 text-white px-2 py-1 rounded text-xs mr-2"
          >
            Clear User Cache
          </button>
          <button
            onClick={logout}
            className="bg-red-500 text-white px-2 py-1 rounded text-xs"
          >
            Logout (Clear & Redirect)
          </button>
        </div>

        <div className="mt-2 text-xs">
          <strong>User Query Keys:</strong>
          <div className="max-h-20 overflow-y-auto">
            {getUserQueries().map((query, index) => (
              <div key={index} className="truncate">
                {JSON.stringify(query.queryKey)}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
