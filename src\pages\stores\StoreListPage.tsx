import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Eye, Pencil, Trash2 } from "lucide-react";
import CreateStoreModal from "@/components/stores/CreateStoreModal";
import React, { useState, useEffect } from "react";
import { useToast } from "@/hooks/useToast";
import { ToastContainer } from "@/components/common/Toast";
import { UserCacheDebug } from "@/components/debug/UserCacheDebug";
import { useGetStoreList } from "@/hooks/store/storeListPaginatedHooks";
import ViewStoreModal from "@/components/stores/ViewStoreModal";
import { useDeleteStore } from "@/hooks/store/deleteStoreHooks";
import {
  <PERSON><PERSON><PERSON><PERSON>og,
  AlertDialog<PERSON>rigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";

interface Store {
  id: string;

  code: string;
  updated: string;
  sku: string;
  country: string;
  status: "Active" | "Inactive" | "pending";
  type: "retail" | "warehouse" | "outlet" | "Grocery" | "Pharmacy";
}

const StoreListingPage = () => {
  const { toasts, removeToast, success, error } = useToast();
  const [currentPage, setCurrentPage] = useState(1);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [editingStore, setEditingStore] = useState<any>(null);
  const recordsPerPage = 10;
  const [searchText, setSearchText] = useState("");
  const [searchParam, setSearchParam] = useState("");
  const [storeToDelete, setStoreToDelete] = useState<number | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Delete store
  const { mutate: deleteStore, isSuccess } = useDeleteStore();

  // Params for API
  const [storeParams, setStoreParams] = useState({
    size: 10,
    page: 1,
    sort_order: "desc" as "asc" | "desc" | 1 | -1,
    location_name: undefined,
  });

  // Fetch store list
  const { data, isError, refetch } = useGetStoreList(storeParams);

  // Update params when search or page changes
  useEffect(() => {
    setStoreParams((prev) => ({
      ...prev,
      page: currentPage,
      location_name: searchParam || undefined,
    }));
  }, [searchParam, currentPage]);

  const handleCreateStore = (data: any) => {
    setShowModal(false);
    refetch();
  };

  const handleEditStore = (data: any) => {
    setEditingStore(null);
    setShowModal(false);
    refetch();
  };

  const handleDeleteStore = (storeId: number) => {
    if (storeToDelete == null) return;

    setIsLoading(true);
    deleteStore(storeToDelete, {
      onSuccess: () => {
        success("Store deleted", "The store was removed successfully.");
        refetch();
      },
      onError: () => {
        error("Delete failed", "Could not delete the store. Please try again.");
      },
      onSettled: () => {
        setIsLoading(false);
        setStoreToDelete(null);
        setShowDeleteDialog(false);
      },
    });
  };

  // Pagination
  const paginatedStores = data?.result?.data || [];
  const totalRecords = data?.result?.total_items || 0;
  const totalPages = Math.ceil(totalRecords / (storeParams.size || 10));

  return (
    <div>
      <Card className="rounded-xl shadow-sm">
        <CardHeader className="pb-2">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <CardTitle className="text-lg">Stores Listing</CardTitle>
            <div className="flex gap-2 w-full md:w-auto">
              <Button
                onClick={() => {
                  setEditingStore(null);
                  setShowModal(true);
                }}
              >
                Create Store
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between mb-4">
            <Input
              type="text"
              placeholder="Search by Store Code"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              className="w-full max-w-sm"
            />
            <div>
              <Button
                onClick={() => {
                  setSearchParam(searchText);
                  setCurrentPage(1);
                }}
              >
                Apply Filter
              </Button>
              <Button
                variant="outline"
                className="ml-3"
                onClick={() => {
                  setSearchText("");
                  setSearchParam("");
                  setCurrentPage(1);
                }}
              >
                Clear
              </Button>
            </div>
          </div>
          <div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="font-semibold">
                    Store Code / Name
                  </TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead>Store Type</TableHead>
                  <TableHead>SKU Count</TableHead>
                  <TableHead>Country</TableHead>
                  <TableHead className="text-center">Status</TableHead>
                  <TableHead className="font-semibold text-center">
                    Action
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedStores.map((store, idx) => (
                  <TableRow key={idx}>
                    <TableCell>{store.location_name}</TableCell>
                    <TableCell>
                      {store.updated_at
                        ? new Date(store.updated_at).toLocaleDateString()
                        : "—"}
                    </TableCell>
                    <TableCell>{store.location_type_name}</TableCell>
                    <TableCell>{store.sku_count}</TableCell>
                    <TableCell>{store.country}</TableCell>
                    <TableCell className="text-center">
                      <Badge
                        variant={
                          store.status === "active" ? "default" : "destructive"
                        }
                        className={
                          store.status === "active"
                            ? "bg-green-100 text-green-700 hover:bg-green-100 text-center"
                            : "bg-red-100 text-red-700 hover:bg-red-100 text-center"
                        }
                      >
                        {store.status?.charAt(0).toUpperCase() +
                          store.status?.slice(1) || "Unknown"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex justify-center">
                        {/* <Button
                          variant="ghost"
                          size="icon"
                          className="hover:bg-gray-100"
                          onClick={() => setShowViewModal(true)} // ✅ only open modal
                        >
                          <Eye className="w-4 h-4" />
                        </Button> */}
                        <Button
                          variant="ghost"
                          size="icon"
                          className="hover:bg-gray-100"
                          onClick={() => {
                            setEditingStore(store);
                            setShowModal(true);
                          }}
                        >
                          <Pencil className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="hover:bg-gray-100"
                          onClick={() => {
                            setStoreToDelete(Number(store.location_id));
                            setShowDeleteDialog(true);
                          }}
                        >
                          <Trash2 className="w-4 h-4 text-red-500" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <div className="flex items-center justify-between mt-6 text-sm text-gray-500">
            <span>
              Showing {(currentPage - 1) * recordsPerPage + 1}-
              {Math.min(currentPage * recordsPerPage, totalRecords)} of{" "}
              {totalRecords} rows
            </span>
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                className="px-2 py-1"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage((prev) => prev - 1)}
              >
                {"<"}
              </Button>
              {[...Array(totalPages)].map((_, i) => (
                <Button
                  key={i}
                  variant={currentPage === i + 1 ? "default" : "ghost"}
                  size="sm"
                  className={`px-3 py-1 ${currentPage === i + 1 ? "bg-primary text-white" : ""
                    }`}
                  onClick={() => setCurrentPage(i + 1)}
                >
                  {i + 1}
                </Button>
              ))}
              <Button
                variant="ghost"
                size="sm"
                className="px-2 py-1"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage((prev) => prev + 1)}
              >
                {">"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      {/* Create/Edit Modal */}
      {showModal && (
        <CreateStoreModal
          isOpen={showModal}
          onClose={() => {
            setShowModal(false);
            setEditingStore(null);
          }}
          onSubmit={editingStore ? handleEditStore : handleCreateStore}
          editingStore={editingStore}
        />
      )}
      {/* View Store Modal */}
      {showViewModal && (
        <ViewStoreModal
          isOpen={showViewModal}
          onClose={() => setShowViewModal(false)}
        />
      )}

      {/* Delete store confirmation popup */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this store? This action is
              irreversible and will permanently remove the store and its
              associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => {
                setShowDeleteDialog(false);
                setStoreToDelete(null);
              }}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction asChild>
              <Button
                type="button"
                disabled={isLoading}
                onClick={() => handleDeleteStore(storeToDelete!)}
                className="min-w-[120px]"
              >
                {isLoading ? (
                  <span className="flex items-center justify-center">
                    <span className="animate-spin mr-2 h-5 w-5 border-2 border-white border-t-transparent rounded-full" />
                    Deleting...
                  </span>
                ) : (
                  "Yes, Delete"
                )}
              </Button>
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />

      {/* Debug Component - Remove in production */}
      <UserCacheDebug />
    </div>
  );
};

export default StoreListingPage;
