import { useState, useEffect, useRef } from 'react';
import { TrendingUp, ZoomIn, ZoomOut, RotateCcw, Download, Eye, EyeOff } from 'lucide-react';

interface ChartDataPoint {
  date: string;
  actual?: number;
  predicted: number;
  confidence_lower: number;
  confidence_upper: number;
}

interface InteractiveForecastChartProps {
  historicalData: ChartDataPoint[];
  forecastData: ChartDataPoint[];
  isLoading?: boolean;
  showActualData?: boolean;
}

export default function InteractiveForecastChart({ 
  historicalData, 
  forecastData, 
  isLoading = false,
  showActualData = false 
}: InteractiveForecastChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [showActual, setShowActual] = useState(true);
  const [showPredicted, setShowPredicted] = useState(true);
  const [showConfidence, setShowConfidence] = useState(true);
  const [zoom, setZoom] = useState(1);
  const [panX, setPanX] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [lastMouseX, setLastMouseX] = useState(0);
  const [hoveredPoint, setHoveredPoint] = useState<ChartDataPoint | null>(null);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });

  const allData = [...historicalData, ...forecastData];
  const maxValue = Math.max(...allData.map(d => Math.max(d.predicted, d.actual || 0, d.confidence_upper)));
  const minValue = Math.min(...allData.map(d => Math.min(d.predicted, d.actual || 0, d.confidence_lower)));
  const padding = (maxValue - minValue) * 0.1;

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (isLoading) {
      // Loading state
      ctx.fillStyle = '#71717a';
      ctx.font = '14px Inter';
      ctx.textAlign = 'center';
      ctx.fillText('Loading chart data...', canvas.width / (2 * window.devicePixelRatio), canvas.height / (2 * window.devicePixelRatio));
      return;
    }

    const chartWidth = rect.width - 80;
    const chartHeight = rect.height - 60;
    const chartLeft = 50;
    const chartTop = 20;

    // Draw grid
    ctx.strokeStyle = '#f1f5f9';
    ctx.lineWidth = 1;
    
    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = chartTop + (chartHeight / 5) * i;
      ctx.beginPath();
      ctx.moveTo(chartLeft, y);
      ctx.lineTo(chartLeft + chartWidth, y);
      ctx.stroke();
    }

    // Vertical grid lines
    const timePoints = allData.length;
    for (let i = 0; i <= 10; i++) {
      const x = chartLeft + (chartWidth / 10) * i;
      ctx.beginPath();
      ctx.moveTo(x, chartTop);
      ctx.lineTo(x, chartTop + chartHeight);
      ctx.stroke();
    }

    // Helper function to get x position
    const getX = (index: number) => {
      return chartLeft + ((index * chartWidth) / (timePoints - 1)) * zoom;
    };

    // Helper function to get y position
    const getY = (value: number) => {
      const normalizedValue = (value - minValue + padding) / (maxValue - minValue + 2 * padding);
      return chartTop + chartHeight - (normalizedValue * chartHeight);
    };

    // Draw confidence interval
    if (showConfidence) {
      ctx.fillStyle = 'rgba(43, 82, 79, 0.1)';
      ctx.beginPath();
      
      // Draw upper confidence line
      allData.forEach((point, index) => {
        const x = getX(index);
        const y = getY(point.confidence_upper);
        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });
      
      // Draw lower confidence line (reverse order)
      for (let i = allData.length - 1; i >= 0; i--) {
        const x = getX(i);
        const y = getY(allData[i].confidence_lower);
        ctx.lineTo(x, y);
      }
      
      ctx.closePath();
      ctx.fill();
    }

    // Draw actual data line
    if (showActual) {
      ctx.strokeStyle = '#2b524f';
      ctx.lineWidth = 3;
      ctx.beginPath();
      
      // Draw actual data from both historical and forecast data if showActualData is true
      const dataToProcess = showActualData ? allData : historicalData;
      
      dataToProcess.forEach((point, index) => {
        if (point.actual !== undefined) {
          const x = getX(index);
          const y = getY(point.actual);
          if (index === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        }
      });
      
      ctx.stroke();

      // Draw actual data points
      ctx.fillStyle = '#2b524f';
      dataToProcess.forEach((point, index) => {
        if (point.actual !== undefined) {
          const x = getX(index);
          const y = getY(point.actual);
          ctx.beginPath();
          ctx.arc(x, y, 4, 0, Math.PI * 2);
          ctx.fill();
        }
      });
    }

    // Draw predicted data line
    if (showPredicted) {
      ctx.strokeStyle = '#16a34a';
      ctx.lineWidth = 3;
      ctx.setLineDash([5, 5]);
      ctx.beginPath();
      
      allData.forEach((point, index) => {
        const x = getX(index);
        const y = getY(point.predicted);
        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });
      
      ctx.stroke();
      ctx.setLineDash([]);

      // Draw predicted data points
      ctx.fillStyle = '#16a34a';
      allData.forEach((point, index) => {
        const x = getX(index);
        const y = getY(point.predicted);
        ctx.beginPath();
        ctx.arc(x, y, 3, 0, Math.PI * 2);
        ctx.fill();
      });
    }

    // Draw divider between historical and forecast
    const dividerX = getX(historicalData.length - 1);
    ctx.strokeStyle = '#ab732b';
    ctx.lineWidth = 2;
    ctx.setLineDash([10, 5]);
    ctx.beginPath();
    ctx.moveTo(dividerX, chartTop);
    ctx.lineTo(dividerX, chartTop + chartHeight);
    ctx.stroke();
    ctx.setLineDash([]);

    // Draw y-axis labels
    ctx.fillStyle = '#71717a';
    ctx.font = '12px Inter';
    ctx.textAlign = 'right';
    for (let i = 0; i <= 5; i++) {
      const value = minValue + ((maxValue - minValue) / 5) * (5 - i);
      const y = chartTop + (chartHeight / 5) * i;
      ctx.fillText(`${Math.round(value).toLocaleString()}`, chartLeft - 10, y + 4);
    }

    // Draw x-axis labels
    ctx.textAlign = 'center';
    const labelInterval = Math.max(1, Math.floor(timePoints / 8));
    allData.forEach((point, index) => {
      if (index % labelInterval === 0) {
        const x = getX(index);
        const date = new Date(point.date);
        const label = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        ctx.fillText(label, x, chartTop + chartHeight + 20);
      }
    });

    // Draw legend labels
    // ctx.font = '11px Inter';
    // ctx.fillStyle = '#374151';
    // ctx.fillText('Historical', chartLeft + 60, chartTop + chartHeight + 45);
    // ctx.fillText('Forecast', chartLeft + 200, chartTop + chartHeight + 45);

  }, [allData, showActual, showPredicted, showConfidence, zoom, panX, isLoading, showActualData]);

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    setMousePos({ x: e.clientX, y: e.clientY });

    if (isDragging) {
      const deltaX = x - lastMouseX;
      setPanX(prev => Math.max(Math.min(prev + deltaX, 100), -200));
      setLastMouseX(x);
      return;
    }

    // Find closest data point
    const chartLeft = 50;
    const chartWidth = rect.width - 80;
    const timePoints = allData.length;
    
    const closestIndex = Math.round(((x - chartLeft - panX) / zoom) * (timePoints - 1) / chartWidth);
    if (closestIndex >= 0 && closestIndex < allData.length) {
      setHoveredPoint(allData[closestIndex]);
    } else {
      setHoveredPoint(null);
    }
  };

  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDragging(true);
    const rect = canvasRef.current?.getBoundingClientRect();
    if (rect) {
      setLastMouseX(e.clientX - rect.left);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev * 1.5, 5));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev / 1.5, 0.5));
  };

  const handleReset = () => {
    setZoom(1);
    setPanX(0);
  };

  const handleExport = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const link = document.createElement('a');
    link.download = 'forecast-chart.png';
    link.href = canvas.toDataURL();
    link.click();
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  return (
    <div style={{ position: 'relative', width: '100%' }}>
      {/* Chart Controls */}
      <div style={{ 
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '0.5rem',
        zIndex: 10
      }}>
        <button
          onClick={() => setShowActual(!showActual)}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.25rem',
            padding: '0.375rem 0.75rem',
            fontSize: '0.75rem',
            fontWeight: '500',
            borderRadius: '0.375rem',
            border: '1px solid #e4e4e7',
            backgroundColor: showActual ? '#2b524f' : 'white',
            color: showActual ? 'white' : '#374151',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}
        >
          {showActual ? <Eye size={12} /> : <EyeOff size={12} />}
          Historical 
        </button>
        
        <button
          onClick={() => setShowPredicted(!showPredicted)}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.25rem',
            padding: '0.375rem 0.75rem',
            fontSize: '0.75rem',
            fontWeight: '500',
            borderRadius: '0.375rem',
            border: '1px solid #e4e4e7',
            backgroundColor: showPredicted ? '#16a34a' : 'white',
            color: showPredicted ? 'white' : '#374151',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}
        >
          {showPredicted ? <Eye size={12} /> : <EyeOff size={12} />}
          Forecast
        </button>
        
        <button
          onClick={() => setShowConfidence(!showConfidence)}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.25rem',
            padding: '0.375rem 0.75rem',
            fontSize: '0.75rem',
            fontWeight: '500',
            borderRadius: '0.375rem',
            border: '1px solid #e4e4e7',
            backgroundColor: showConfidence ? '#71717a' : 'white',
            color: showConfidence ? 'white' : '#374151',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}
        >
          {showConfidence ? <Eye size={12} /> : <EyeOff size={12} />}
          Confidence
        </button>
      </div>



      {/* Main Chart Canvas */}
      <div className='overflow-hidden'>
        <canvas
        ref={canvasRef}
        style={{
          width: 'calc(100% + 25px)',
          height: '300px',
          borderRadius: '0.375rem',
        }}
        onMouseMove={handleMouseMove}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseLeave={() => {
          setHoveredPoint(null);
          setIsDragging(false);
        }}
      />
      </div>

      {/* Tooltip */}
      {hoveredPoint && (
        <div
          style={{
            position: 'fixed',
            left: mousePos.x + 10,
            top: mousePos.y - 10,
            backgroundColor: 'rgba(0, 0, 0, 0.9)',
            color: 'white',
            padding: '0.75rem',
            borderRadius: '0.375rem',
            fontSize: '0.75rem',
            zIndex: 1000,
            pointerEvents: 'none',
            transform: 'translate(0, -100%)'
          }}
        >
          <div style={{ fontWeight: '600', marginBottom: '0.25rem' }}>
            {new Date(hoveredPoint.date).toLocaleDateString()}
          </div>
          {hoveredPoint.actual !== undefined && (
            <div style={{ color: '#60a5fa' }}>
              Historical: {hoveredPoint.actual}
            </div>
          )}
          <div style={{ color: '#34d399' }}>
            Forcasted: {hoveredPoint.predicted}
          </div>
          <div style={{ color: '#d1d5db', fontSize: '0.625rem' }}>
            Upper Bound: {hoveredPoint.confidence_upper} 
          </div>
           <div style={{ color: '#d1d5db', fontSize: '0.625rem' }}>
            Lower Bound:  {hoveredPoint.confidence_lower}
          </div>
        </div>
      )}

      {/* Chart Legend */}
      <div style={{
        display: 'flex',
        gap: '1rem',
        fontSize: '0.75rem',
        justifyContent: 'center',
        color: '#71717a',
        marginTop: '1rem',
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
          <div style={{ width: '12px', height: '3px', backgroundColor: '#2b524f' }}></div>
          Historical 
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
          <div style={{ width: '12px', height: '3px', backgroundColor: '#16a34a', borderStyle: 'dashed' }}></div>
          Forecast
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
          <div style={{ width: '12px', height: '8px', backgroundColor: 'rgba(43, 82, 79, 0.2)' }}></div>
          Confidence Interval
        </div>
      </div>

      {/* Zoom Controls */}
      <div style={{ 
        display: 'flex',
        justifyContent: 'flex-end', 
        gap: '0.25rem',
        zIndex: 10
      }}>
        <button
          onClick={handleZoomIn}
          style={{
            padding: '0.5rem',
            borderRadius: '0.375rem',
            border: '1px solid #e4e4e7',
            backgroundColor: 'white',
            color: '#374151',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}
          title="Zoom In"
        >
          <ZoomIn size={14} />
        </button>
        
        <button
          onClick={handleZoomOut}
          style={{
            padding: '0.5rem',
            borderRadius: '0.375rem',
            border: '1px solid #e4e4e7',
            backgroundColor: 'white',
            color: '#374151',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}
          title="Zoom Out"
        >
          <ZoomOut size={14} />
        </button>
        
        <button
          onClick={handleReset}
          style={{
            padding: '0.5rem',
            borderRadius: '0.375rem',
            border: '1px solid #e4e4e7',
            backgroundColor: 'white',
            color: '#374151',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}
          title="Reset View"
        >
          <RotateCcw size={14} />
        </button>
        
        <button
          onClick={handleExport}
          style={{
            padding: '0.5rem',
            borderRadius: '0.375rem',
            border: '1px solid #e4e4e7',
            backgroundColor: 'white',
            color: '#374151',
            cursor: 'pointer',
            transition: 'all 0.2s'
          }}
          title="Export Chart"
        >
          <Download size={14} />
        </button>
      </div>

    </div>
  );
}