import useDataService from '../../services/useDataService';
import { GET_DASHBOARD_DATA } from '../../constants/urls';
import { usePageQuery } from '../useRouteQuery';
import { useDebugQuery } from '../useDebugQuery';

export const useGetDashboardData = (enabled = true) => {
    const queryKey = ['dashboard-data'];

    // Debug logging - remove this in production
    useDebugQuery('useGetDashboardData', queryKey, enabled);

    return usePageQuery(
        queryKey,
        async () => {
            console.log('🚀 Dashboard API call triggered!');
            const endpoint = `${GET_DASHBOARD_DATA}`;
            return await useDataService.getService(endpoint);
        },
        {
            enabled: enabled,
            staleTime: 5 * 60 * 1000, // Cache for 5 minutes
        }
    );
};