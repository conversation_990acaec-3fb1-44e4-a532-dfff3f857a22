# Solution: Cross-User Cache Contamination Fix

## Problem Identified
The real issue was **cross-user data contamination** in React Query cache:

1. **User A** logs in and visits `/data-ingestion/batches` → API call fetches User A's data
2. **User A** logs out (without page refresh)
3. **User B** logs in and visits `/data-ingestion/batches` → **NO API call** because React Query serves cached data from User A
4. **User B** sees User A's data until page refresh

### Root Cause
Query keys didn't include user identifiers, so React Query treated different users' data as the same:
```typescript
// ❌ WRONG - Same cache key for all users
queryKey: ['batch-list', page, size, sortOrder]

// ✅ CORRECT - User-specific cache key
queryKey: ['batch-list', page, size, sortOrder, 'user', user_id]
```

## Solution Implemented

### 1. Updated `useRouteQuery` Hook - User-Aware Caching
**File: `src/hooks/useRouteQuery.ts`**

This custom hook now ensures:
- ✅ **User-specific query keys** - Automatically includes `user_id` in all query keys
- ✅ **Prevents cross-user contamination** - Each user gets their own cache space
- ✅ **Authentication-aware** - Only runs queries when user is authenticated
- ✅ **Automatic cache isolation** - Different users can't see each other's data

```typescript
// Automatically creates user-specific keys:
// Input:  ['batch-list', page, size]
// Output: ['batch-list', page, size, 'user', 'user123']
const userSpecificQueryKey = user?.user_id
  ? [...options.queryKey, 'user', user.user_id]
  : options.queryKey;
```

### 2. Updated Auth Store - Cache Clearing on Logout
**File: `src/store/authStore.ts`**
- ✅ **Automatic cache clearing** - Removes user-specific cache on logout
- ✅ **Prevents data leakage** - Ensures next user doesn't see previous user's data

### 3. Created Auth Actions Hook
**File: `src/hooks/useAuthActions.ts`**
- ✅ **Proper logout handling** - Clears cache before logout
- ✅ **Comprehensive cache cleanup** - Removes all user-specific and sensitive data
- ✅ **Navigation handling** - Redirects to login after logout

### 4. Updated Your Hooks with User Context

#### Dashboard Hook
**File: `src/hooks/dashboard/useDashboardData.ts`**
- ✅ **User-specific caching** - Query key now includes user ID
- ✅ **Debug logging** - Shows which user is making the API call
- ✅ **Automatic triggering** - API calls when landing on route

#### Batch List Hook
**File: `src/hooks/batchList/batchListHooks.ts`**
- ✅ **User-specific caching** - Each user gets their own batch list cache
- ✅ **Enhanced debugging** - Shows user context in console logs
- ✅ **Filter handling** - Proper parameter management

#### Store List Hook
**File: `src/hooks/store/storeListHooks.ts`**
- ✅ **User-specific caching** - Store list cached per user
- ✅ **Simplified interface** - Clean parameter handling

### 5. Enhanced Debug Hook
**File: `src/hooks/useDebugQuery.ts`**
- ✅ **User context logging** - Shows which user is making requests
- ✅ **Cache key inspection** - Displays user-specific query keys
- ✅ **User change tracking** - Logs when user context changes

## How to Use

### For Existing Hooks
Replace your `useQuery` calls with `usePageQuery`:

```typescript
// OLD WAY ❌
import { useQuery } from '@tanstack/react-query';

export const useGetSomeData = () => {
  return useQuery({
    queryKey: ['some-data'],
    queryFn: async () => {
      return await api.getSomeData();
    },
  });
};

// NEW WAY ✅
import { usePageQuery } from '../useRouteQuery';

export const useGetSomeData = () => {
  return usePageQuery(
    ['some-data'],
    async () => {
      console.log('🚀 API call triggered!');
      return await api.getSomeData();
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes cache
    }
  );
};
```

### For New Page Hooks
Follow this pattern:

```typescript
// src/hooks/yourPage/useYourPageData.ts
import { usePageQuery } from '../useRouteQuery';
import { useDebugQuery } from '../useDebugQuery';
import useDataService from '@/services/useDataService';

export const useGetYourPageData = (enabled = true) => {
  const queryKey = ['your-page-data'];
  
  // Debug logging (remove in production)
  useDebugQuery('useGetYourPageData', queryKey, enabled);
  
  return usePageQuery(
    queryKey,
    async () => {
      console.log('🚀 Your Page API call triggered!');
      const endpoint = '/api/your-endpoint';
      return await useDataService.getService(endpoint);
    },
    {
      enabled: enabled,
      staleTime: 5 * 60 * 1000,
    }
  );
};
```

## Testing the Cross-User Fix

### 1. Test Cross-User Scenario (Your Original Issue)
**Step-by-step test:**

1. **Login as User A**
   ```
   🔍 [useGetBatchList] Current user: userA123 <EMAIL>
   🚀 Batch List API call triggered for user: userA123 (<EMAIL>)
   ```

2. **Navigate to `/data-ingestion/batches`**
   - Should see User A's batch data
   - Check console for user-specific query key: `['batch-list', 1, 10, 'asc', '', '', '', 'user', 'userA123']`

3. **Logout (without page refresh)**
   ```
   🚪 Logging out user: userA123 (<EMAIL>)
   🧹 Cleared all cache for user: userA123
   ```

4. **Login as User B**
   ```
   🔍 [useGetBatchList] Current user: userB456 <EMAIL>
   👤 [useGetBatchList] User changed: userB456 <EMAIL>
   ```

5. **Navigate to `/data-ingestion/batches`**
   ```
   🚀 Batch List API call triggered for user: userB456 (<EMAIL>)
   📡 Batch List API endpoint: /api/batch-list?page=1&size=10&sort_order=asc
   ```
   - ✅ **Should see fresh API call** (not cached data)
   - ✅ **Should see User B's data** (not User A's data)
   - ✅ **Query key includes User B's ID**: `['batch-list', 1, 10, 'asc', '', '', '', 'user', 'userB456']`

### 2. Check Browser Console Logs
You should see user-specific logging:
```
🔍 [useGetBatchList] Hook mounted on route: /data-ingestion/batches
🔍 [useGetBatchList] Current user: userB456 <EMAIL>
⚙️ [useGetBatchList] Query key changed: ['batch-list', 1, 10, 'asc', '', '', '', 'user', 'userB456']
🚀 Batch List API call triggered for user: userB456 (<EMAIL>)
```

### 3. Check Network Tab
- **User A login** → API calls show User A's data
- **User A logout** → No API calls (cache cleared)
- **User B login** → Fresh API calls for User B's data
- **No cached responses** → All requests should be fresh for new user

### 4. Verify Cache Isolation
Open React Query DevTools (if enabled) and verify:
- User A's queries are cleared after logout
- User B gets fresh query keys with their user ID
- No cross-contamination between user caches

## How to Use the New Auth Actions

### Replace Direct Logout Calls
**OLD WAY ❌**
```typescript
import { useAuthStore } from '../store/authStore';

const { logout } = useAuthStore();
// This doesn't clear cache properly
logout();
```

**NEW WAY ✅**
```typescript
import { useAuthActions } from '../hooks/useAuthActions';

const { logout } = useAuthActions();
// This clears cache and handles logout properly
logout();
```

### Update Your Logout Components
Find all places where you call logout and update them:

```typescript
// In your header/navigation component
import { useAuthActions } from '@/hooks/useAuthActions';

export function Header() {
  const { logout } = useAuthActions();

  const handleLogout = () => {
    logout(); // This will clear cache and redirect
  };

  return (
    <button onClick={handleLogout}>
      Logout
    </button>
  );
}
```

## Key Benefits

1. **🔒 Data Security**: Users can never see each other's data
2. **🚀 Automatic API Calls**: Fresh data on every user switch
3. **🧹 Clean Cache Management**: Automatic cleanup on logout
4. **👤 User-Aware Caching**: Each user gets isolated cache space
5. **🐛 Better Debugging**: Clear logging shows user context
6. **⚡ Performance**: Proper caching within user sessions

## Immediate Action Required

### 1. Update All Logout Calls
Find and replace all direct `authStore.logout()` calls with `useAuthActions().logout()`:

```bash
# Search for these patterns in your codebase:
- useAuthStore().logout
- authStore.logout
- logout() // from useAuthStore
```

### 2. Test the Cross-User Scenario
1. Login as User A → Visit batch list page
2. Logout (don't refresh) → Login as User B
3. Visit batch list page → Should see fresh API call for User B
4. Check console logs for user-specific query keys

### 3. Apply to Remaining Hooks
Update these hooks to use the new pattern:

```typescript
// src/hooks/forcastList/useForcastListHooks.ts
export const useGetForcastList = (page, size, sortOrder, filters) => {
  const { user } = useAuthStore();
  const queryKey = ["forecast-list", page, size, sortOrder, filters];

  useDebugQuery('useGetForcastList', queryKey, true);

  return usePageQuery(
    queryKey,
    async () => {
      console.log(`🚀 Forecast List API call for user: ${user?.user_id}`);
      // ... your API call
    },
    { staleTime: 5 * 60 * 1000 }
  );
};
```

### 4. Remove Debug Logging (Production)
Once everything works, remove debug calls:
```typescript
// Remove these lines in production:
useDebugQuery('HookName', queryKey, enabled);
console.log('🚀 API call triggered...');
```

## Example: Converting More Hooks

For your forecast list hook:
```typescript
// src/hooks/forcastList/useForcastListHooks.ts
import { usePageQuery } from "../useRouteQuery";

export const useGetForcastList = (
  page: number,
  size: number,
  sortOrder: string,
  filters: { forecastName?: any; store?: any }
) => {
  return usePageQuery(
    ["forecast-list", page, size, sortOrder, filters],
    async () => {
      let endpoint = `${GET_FORCAST_LIST_URL}?page=${page}&size=${size}&sort_order=${sortOrder}`;
      if (filters?.forecastName) {
        endpoint += `&forecast_id=${encodeURIComponent(filters.forecastName)}`;
      }
      if (filters?.store) {
        endpoint += `&location_id=${encodeURIComponent(filters.store)}`;
      }
      return await useDataService.getService(endpoint);
    },
    {
      staleTime: 5 * 60 * 1000,
    }
  );
};
```

This solution should resolve your API call timing issues when navigating to routes!
