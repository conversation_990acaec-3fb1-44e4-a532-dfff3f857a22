# Solution: API Calls Triggering on Route Navigation

## Problem
Your hooks were not consistently triggering API calls when landing on routes. This was due to:

1. **Missing or incorrect queryKey** - Empty arrays or non-unique keys
2. **React Query configuration issues** - Incorrect `enabled`, `refetchOnMount` settings
3. **Component mounting timing** - Queries not waiting for component to fully mount

## Solution Implemented

### 1. Created `useRouteQuery` Hook
**File: `src/hooks/useRouteQuery.ts`**

This custom hook ensures:
- ✅ Queries only run after component mounts
- ✅ Proper route-aware behavior
- ✅ Consistent configuration across all page queries
- ✅ Better error handling and caching

### 2. Updated Your Hooks

#### Dashboard Hook
**File: `src/hooks/dashboard/useDashboardData.ts`**
- ✅ Now uses `usePageQuery` for automatic route triggering
- ✅ Added debug logging to track API calls
- ✅ Proper queryKey: `['dashboard-data']`

#### Batch List Hook  
**File: `src/hooks/batchList/batchListHooks.ts`**
- ✅ Now uses `usePageQuery` for automatic route triggering
- ✅ Improved parameter handling for filters
- ✅ Proper queryKey with all dependencies

#### Store List Hook
**File: `src/hooks/store/storeListHooks.ts`**
- ✅ Now uses `usePageQuery` for automatic route triggering
- ✅ Simplified parameter interface
- ✅ Proper TypeScript typing

### 3. Added Debug Hook
**File: `src/hooks/useDebugQuery.ts`**

Use this to debug API call issues:
```typescript
// Add to any hook temporarily
useDebugQuery('HookName', queryKey, enabled);
```

## How to Use

### For Existing Hooks
Replace your `useQuery` calls with `usePageQuery`:

```typescript
// OLD WAY ❌
import { useQuery } from '@tanstack/react-query';

export const useGetSomeData = () => {
  return useQuery({
    queryKey: ['some-data'],
    queryFn: async () => {
      return await api.getSomeData();
    },
  });
};

// NEW WAY ✅
import { usePageQuery } from '../useRouteQuery';

export const useGetSomeData = () => {
  return usePageQuery(
    ['some-data'],
    async () => {
      console.log('🚀 API call triggered!');
      return await api.getSomeData();
    },
    {
      staleTime: 5 * 60 * 1000, // 5 minutes cache
    }
  );
};
```

### For New Page Hooks
Follow this pattern:

```typescript
// src/hooks/yourPage/useYourPageData.ts
import { usePageQuery } from '../useRouteQuery';
import { useDebugQuery } from '../useDebugQuery';
import useDataService from '@/services/useDataService';

export const useGetYourPageData = (enabled = true) => {
  const queryKey = ['your-page-data'];
  
  // Debug logging (remove in production)
  useDebugQuery('useGetYourPageData', queryKey, enabled);
  
  return usePageQuery(
    queryKey,
    async () => {
      console.log('🚀 Your Page API call triggered!');
      const endpoint = '/api/your-endpoint';
      return await useDataService.getService(endpoint);
    },
    {
      enabled: enabled,
      staleTime: 5 * 60 * 1000,
    }
  );
};
```

## Testing the Solution

### 1. Check Browser Console
When you navigate to pages, you should see:
```
🔍 [useGetDashboardData] Hook mounted on route: /dashboard
🔍 [useGetDashboardData] Query key: ['dashboard-data']
🔍 [useGetDashboardData] Enabled: true
🚀 Dashboard API call triggered!
```

### 2. Check Network Tab
- Navigate to `/dashboard` → Should see API call to dashboard endpoint
- Navigate to `/data-ingestion/batches` → Should see API call to batch list endpoint
- Navigate to `/stores` → Should see API call to store list endpoint

### 3. Test Route Changes
- Navigate between different routes
- API calls should trigger automatically when landing on each route
- Data should load without manual refresh

## Key Benefits

1. **Automatic Triggering**: API calls happen automatically when you land on routes
2. **Consistent Behavior**: All page-level hooks behave the same way
3. **Better Caching**: Proper cache management with 5-minute stale time
4. **Debug Support**: Easy debugging with console logs
5. **TypeScript Support**: Full type safety maintained

## Next Steps

1. **Test the current implementation** on your dashboard and batch list pages
2. **Apply the same pattern** to other page hooks (forecast list, company details, etc.)
3. **Remove debug logging** once everything works correctly
4. **Consider adding error boundaries** for better error handling

## Example: Converting More Hooks

For your forecast list hook:
```typescript
// src/hooks/forcastList/useForcastListHooks.ts
import { usePageQuery } from "../useRouteQuery";

export const useGetForcastList = (
  page: number,
  size: number,
  sortOrder: string,
  filters: { forecastName?: any; store?: any }
) => {
  return usePageQuery(
    ["forecast-list", page, size, sortOrder, filters],
    async () => {
      let endpoint = `${GET_FORCAST_LIST_URL}?page=${page}&size=${size}&sort_order=${sortOrder}`;
      if (filters?.forecastName) {
        endpoint += `&forecast_id=${encodeURIComponent(filters.forecastName)}`;
      }
      if (filters?.store) {
        endpoint += `&location_id=${encodeURIComponent(filters.store)}`;
      }
      return await useDataService.getService(endpoint);
    },
    {
      staleTime: 5 * 60 * 1000,
    }
  );
};
```

This solution should resolve your API call timing issues when navigating to routes!
