import { useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '../store/authStore';
import { useNavigate } from 'react-router-dom';

/**
 * Hook that provides auth actions with proper cache management
 * Use this instead of calling authStore.logout() directly
 */
export function useAuthActions() {
  const queryClient = useQueryClient();
  const { logout: authLogout, user } = useAuthStore();
  const navigate = useNavigate();

  const logout = () => {
    const currentUser = user;
    
    if (currentUser?.user_id) {
      console.log(`🚪 Logging out user: ${currentUser.user_id} (${currentUser.email})`);
      
      // Clear all user-specific cache
      queryClient.removeQueries({
        predicate: (query: any) => {
          return query.queryKey.includes('user') && query.queryKey.includes(currentUser.user_id);
        }
      });
      
      // Also clear any queries that might contain user-specific data
      queryClient.removeQueries({
        predicate: (query: any) => {
          const keyString = JSON.stringify(query.queryKey);
          return keyString.includes('batch-list') || 
                 keyString.includes('dashboard-data') || 
                 keyString.includes('store-list') ||
                 keyString.includes('forecast-list');
        }
      });
      
      console.log(`🧹 Cleared all cache for user: ${currentUser.user_id}`);
    }
    
    // Call the auth store logout
    authLogout();
    
    // Navigate to login
    navigate('/auth/login');
  };

  return {
    logout,
  };
}
