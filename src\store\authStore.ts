import { create } from 'zustand';
import { QueryClient } from '@tanstack/react-query';

export interface User {
  user_id: string;
  email: string;
  username: string;
  tenant_id: string;
  access_token?: string;
  full_name: string;
  role: string;
  avatar?: string
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (user: User) => void;
  logout: (queryClient?: QueryClient) => void;
  setLoading: (loading: boolean) => void;

}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  isAuthenticated: false,
  isLoading: false,

  login: (user: User) => {
    set({ user, isAuthenticated: true, isLoading: false });
    // Save to localStorage
    localStorage.setItem('flashana-user', JSON.stringify(user));
    localStorage.setItem('flashana-authenticated', 'true');
  },

  logout: (queryClient?: QueryClient) => {
    const currentUser = useAuthStore.getState().user;

    // Clear user-specific cache if queryClient is provided
    if (queryClient && currentUser?.user_id) {
      queryClient.removeQueries({
        predicate: (query: any) => {
          return query.queryKey.includes('user') && query.queryKey.includes(currentUser.user_id);
        }
      });
      console.log(`🧹 Cleared cache for user: ${currentUser.user_id}`);
    }

    set({ user: null, isAuthenticated: false, isLoading: false });
    // Clear localStorage
    localStorage.removeItem('flashana-user');
    localStorage.removeItem('flashana-authenticated');
  },

  setLoading: (isLoading: boolean) => {
    set({ isLoading });
  },
}));

// Initialize auth state from localStorage on app load
if (typeof window !== 'undefined') {
  const savedUser = localStorage.getItem('flashana-user');
  const isAuthenticated = localStorage.getItem('flashana-authenticated') === 'true';

  if (savedUser && isAuthenticated) {
    try {
      const user = JSON.parse(savedUser);
      useAuthStore.getState().login(user);
    } catch (error) {
      console.error('Failed to parse saved user data:', error);
      localStorage.removeItem('flashana-user');
      localStorage.removeItem('flashana-authenticated');
    }
  }
}