import { useMutation } from '@tanstack/react-query';
import useDataService from '../../services/useDataService';
import { GET_STORELIST_URL } from '../../constants/urls';
import { usePageQuery } from '../useRouteQuery';
import { useDebugQuery } from '../useDebugQuery';
import { useAuthStore } from '../../store/authStore';

interface StoreListParams {
  location_name?: string;
  page?: number;
  size?: number;
  sort_column?: string;
  sort_order?: 'asc' | 'desc' | 1 | -1;
}

export interface Store {
  id: string;
  location_name: string;
  [key: string]: any;
}

export interface StoreListResponse {
  data: Store[];
  total: number;
  page: number;
  size: number;
  [key: string]: any;
}

export const useGetStoreList = (params: StoreListParams, enabled = true) => {
  const { user } = useAuthStore();
  const queryKey = ['store-list-paginated', params];

  // Debug logging - remove this in production
  useDebugQuery('useGetStoreList (Paginated)', queryKey, enabled);

  return usePageQuery<StoreListResponse>(
    queryKey,
    async () => {
      console.log(`🚀 Store List (Paginated) API call triggered for user: ${user?.user_id} (${user?.email})`);
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, String(value));
        }
      });
      const endpoint = `${GET_STORELIST_URL}?${searchParams.toString()}`;
      console.log(`📡 Store List (Paginated) API endpoint: ${endpoint}`);
      return await useDataService.getService(endpoint);
    },
    {
      enabled: enabled,
      staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    }
  );
};