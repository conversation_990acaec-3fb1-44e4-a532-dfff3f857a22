import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { format } from "date-fns";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";

// 1️⃣ Table config with filters
const tableConfig = {
  sales_data: {
    columns: [
      "sale_date",
      "sku_id",
      "location_id",
      "quantity",
      "price_per_unit",
      "total_amount",
    ],
    filters: ["Start_date", "End_date", "Location"],
  },
  skus: {
    columns: ["sku_id", "sku_code", "product_id"],
    filters: ["sku_code"],
  },
  products: {
    columns: ["product_id", "product_name"],
    filters: ["product_name"],
  },
  locations: {
    columns: ["location_id", "location_name", "city", "state"],
    filters: ["location_name"],
  },
};

// 2️⃣ Dummy data (to mimic API response)
const mockApiData = {
  sales_data: [
    {
      sale_date: "2025-01-01",
      sku_id: "SKU101",
      location_id: "LOC01",
      quantity: 50,
      price_per_unit: 100,
      total_amount: 5000,
    },
    {
      sale_date: "2025-01-10",
      sku_id: "SKU102",
      location_id: "LOC02",
      quantity: 20,
      price_per_unit: 200,
      total_amount: 4000,
    },
  ],
  skus: [
    { sku_id: "SKU101", sku_code: "IP15PRO", product_id: "PROD01" },
    { sku_id: "SKU102", sku_code: "MB2025", product_id: "PROD02" },
  ],
  products: [
    { product_id: "PROD01", product_name: "iPhone 15 Pro" },
    { product_id: "PROD02", product_name: "MacBook Air" },
  ],
  locations: [
    {
      location_id: "LOC01",
      location_name: "Mumbai Central",
      city: "Mumbai",
      state: "Maharashtra",
    },
    {
      location_id: "LOC02",
      location_name: "Bangalore Hub",
      city: "Bangalore",
      state: "Karnataka",
    },
  ],
};

export default function ImportedDataPage() {
  // 3️⃣ UI control states
  const [pendingTable, setPendingTable] = useState("sales_data");
  const [pendingFilters, setPendingFilters] = useState({});
  const [activeTable, setActiveTable] = useState("sales_data");
  const [activeFilters, setActiveFilters] = useState({});
  const [tableData, setTableData] = useState(mockApiData["sales_data"]);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const handleTableChange = (val: string) => {
    setPendingTable(val);
    setPendingFilters({});
  };

  const handleFilterChange = (key: string, value: string | Date | null) => {
    setPendingFilters((prev) => ({ ...prev, [key]: value }));
  };

  const handleApplyFilter = () => {
    const rawData = mockApiData[pendingTable] || [];

    const filtered = rawData.filter((item) => {
      return Object.entries(pendingFilters).every(([key, val]) => {
        if (!val) return true;

        if (key === "start_date") {
          return new Date(item.sale_date) >= new Date(val as string);
        }
        if (key === "end_date") {
          return new Date(item.sale_date) <= new Date(val as string);
        }
        return item[key]
          ?.toString()
          .toLowerCase()
          .includes(val.toString().toLowerCase());
      });
    });

    setActiveTable(pendingTable);
    setActiveFilters(pendingFilters);
    setTableData(filtered);
    setCurrentPage(1);
  };

  const pagedData = tableData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  const totalPages = Math.ceil(tableData.length / itemsPerPage);

  return (
    <>
      {/* Table Selector */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Imported Data</CardTitle>
        </CardHeader>
        <CardContent>
          <Label htmlFor="table-select">Table Name</Label>
          <Select value={pendingTable} onValueChange={handleTableChange}>
            <SelectTrigger className="w-full max-w-sm">
              <SelectValue placeholder="Select table" />
            </SelectTrigger>
            <SelectContent>
              {Object.keys(tableConfig).map((table) => (
                <SelectItem key={table} value={table}>
                  {table}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Filter & Table Section */}
      <Card>
        <CardHeader>
          <CardTitle>{activeTable}</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-wrap items-end gap-4 mb-4">
            {tableConfig[pendingTable]?.filters.map((filterKey) => (
              <div key={filterKey} className="flex-1">
                <Label>{filterKey.replace("_", " ")}</Label>
                {filterKey === "start_date" || filterKey === "end_date" ? (
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        {pendingFilters[filterKey]
                          ? format(new Date(pendingFilters[filterKey]), "PPP")
                          : `Pick ${filterKey.replace("_", " ")}`}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={
                          pendingFilters[filterKey]
                            ? new Date(pendingFilters[filterKey])
                            : undefined
                        }
                        onSelect={(date) =>
                          handleFilterChange(
                            filterKey,
                            date ? date.toISOString().slice(0, 10) : ""
                          )
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                ) : (
                  <Input
                    placeholder={`Enter ${filterKey}`}
                    value={pendingFilters[filterKey] || ""}
                    onChange={(e) =>
                      handleFilterChange(filterKey, e.target.value)
                    }
                  />
                )}
              </div>
            ))}
            <div className="flex items-end flex-1 justify-end">
              <Button onClick={handleApplyFilter}>Apply Filter</Button>
            </div>
          </div>

          {/* Table */}
          <div className="rounded-md border overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead>
                <tr className="bg-zinc-50">
                  {tableConfig[activeTable]?.columns.map((col) => (
                    <th key={col} className="px-4 py-2 text-left font-medium">
                      {col}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {pagedData.map((row, idx) => (
                  <tr key={idx} className="border-b last:border-b-0">
                    {tableConfig[activeTable]?.columns.map((col) => (
                      <td key={col} className="px-4 py-2">
                        {row[col]}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between mt-4">
            <span className="text-sm text-muted-foreground">
              Showing {(currentPage - 1) * itemsPerPage + 1} -{" "}
              {Math.min(currentPage * itemsPerPage, tableData.length)} of{" "}
              {tableData.length} rows
            </span>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (page) => (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setCurrentPage(page)}
                  >
                    {page}
                  </Button>
                )
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={() =>
                  setCurrentPage((p) => Math.min(totalPages, p + 1))
                }
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
}

// Replace with actual api code
// If api is get type
// GET /api/imported-data?table=sales_data&start_date=2025-01-01&end_date=2025-01-31&location=Mumbai

// // src/api/importedDataApi.ts
// import axios from "axios";

// export async function fetchImportedData(table: string, filters: Record<string, any>) {
//   const response = await axios.get("/api/imported-data", {
//     params: {
//       table,
//       ...filters,
//     },
//   });

//   return response.data; // array of rows
// }

// replace

// const rawData = mockApiData[pendingTable] || [];
// const filtered = rawData.filter(...);
// setTableData(filtered);

// with

// import { fetchImportedData } from "@/api/importedDataApi"; // add at top of file

// const handleApplyFilter = async () => {
//   try {
//     const data = await fetchImportedData(pendingTable, pendingFilters);
//     setActiveTable(pendingTable);
//     setActiveFilters(pendingFilters);
//     setTableData(data);
//     setCurrentPage(1);
//   } catch (error) {
//     console.error("Error fetching table data", error);
//     // optionally: show toast or fallback UI
//   }
// };/

// If api is post type
// POST /api/imported-data
// {
//   "table": "sales_data",
//   "filters": {
//     "start_date": "2025-01-01",
//     "end_date": "2025-01-31",
//     "location": "Mumbai"
//   }
// }

// // src/api/importedDataApi.ts
// import axios from "axios";
// /**
//  * Fetch imported data by sending table name and filters to backend.
//  * Uses POST request with JSON body.
//  */
// export async function fetchImportedData(table: string, filters: Record<string, any>) {
//   const response = await axios.post("/api/imported-data", {
//     table,
//     filters,
//   });

//   return response.data; // assumed to be an array of rows
// }

// replace with

// import { fetchImportedData } from "@/api/importedDataApi";

// const handleApplyFilter = async () => {
//   try {
//     const data = await fetchImportedData(pendingTable, pendingFilters);
//     setActiveTable(pendingTable);
//     setActiveFilters(pendingFilters);
//     setTableData(data);
//     setCurrentPage(1);
//   } catch (error) {
//     console.error("Failed to fetch imported data:", error);
//     // Optionally show a toast or fallback
//   }
// };
