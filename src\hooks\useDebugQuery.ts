import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';

/**
 * Debug hook to help track when API calls should trigger
 * Use this temporarily to debug your API call issues
 * Now includes user information to debug cross-user cache issues
 */
export function useDebugQuery(hookName: string, queryKey: readonly unknown[], enabled: boolean = true) {
  const location = useLocation();
  const { user } = useAuthStore();

  useEffect(() => {
    console.log(`🔍 [${hookName}] Hook mounted on route: ${location.pathname}`);
    console.log(`🔍 [${hookName}] Current user:`, user?.user_id, user?.email);
    console.log(`🔍 [${hookName}] Query key:`, queryKey);
    console.log(`🔍 [${hookName}] Enabled:`, enabled);
  }, []);

  useEffect(() => {
    console.log(`🔄 [${hookName}] Route changed to: ${location.pathname}`);
  }, [location.pathname]);

  useEffect(() => {
    console.log(`⚙️ [${hookName}] Query key changed:`, queryKey);
    console.log(`👤 [${hookName}] User context:`, user?.user_id, user?.email);
  }, [JSON.stringify(queryKey)]);

  useEffect(() => {
    console.log(`🎛️ [${hookName}] Enabled changed:`, enabled);
  }, [enabled]);

  useEffect(() => {
    console.log(`👤 [${hookName}] User changed:`, user?.user_id, user?.email);
  }, [user?.user_id]);
}
