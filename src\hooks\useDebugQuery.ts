import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Debug hook to help track when API calls should trigger
 * Use this temporarily to debug your API call issues
 */
export function useDebugQuery(hookName: string, queryKey: readonly unknown[], enabled: boolean = true) {
  const location = useLocation();

  useEffect(() => {
    console.log(`🔍 [${hookName}] Hook mounted on route: ${location.pathname}`);
    console.log(`🔍 [${hookName}] Query key:`, queryKey);
    console.log(`🔍 [${hookName}] Enabled:`, enabled);
  }, []);

  useEffect(() => {
    console.log(`🔄 [${hookName}] Route changed to: ${location.pathname}`);
  }, [location.pathname]);

  useEffect(() => {
    console.log(`⚙️ [${hookName}] Query key changed:`, queryKey);
  }, [JSON.stringify(queryKey)]);

  useEffect(() => {
    console.log(`🎛️ [${hookName}] Enabled changed:`, enabled);
  }, [enabled]);
}
