import useDataService from "../../services/useDataService";
import { GET_FORCAST_LIST_URL } from "../../constants/urls";
import { usePageQuery } from "../useRouteQuery";
import { useDebugQuery } from "../useDebugQuery";
import { useAuthStore } from "../../store/authStore";
export const useGetForcastList = (
  page: number,
  size: number,
  sortOrder: string,
  filters: { forecastName?: any; store?: any }
) => {
  const { user } = useAuthStore();
  const queryKey = ["forecast-list", page, size, sortOrder, filters]; // Fixed: was "batch-list"

  // Debug logging - remove this in production
  useDebugQuery('useGetForcastList', queryKey, true);

  return usePageQuery(
    queryKey,
    async () => {
      console.log(`🚀 Forecast List API call triggered for user: ${user?.user_id} (${user?.email})`);
      let endpoint = `${GET_FORCAST_LIST_URL}?page=${page}&size=${size}&sort_order=${sortOrder}`;
      if (filters?.forecastName) {
        endpoint += `&forecast_id=${encodeURIComponent(filters.forecastName)}`;
      }
      if (filters?.store) {
        endpoint += `&location_id=${encodeURIComponent(filters.store)}`;
      }
      console.log(`📡 Forecast List API endpoint: ${endpoint}`);
      return await useDataService.getService(endpoint);
    },
    {
      staleTime: 5 * 60 * 1000, // Cache for 5 minutes instead of infinity
    }
  );
};

