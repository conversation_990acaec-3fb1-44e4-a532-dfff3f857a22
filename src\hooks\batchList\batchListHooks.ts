// src/hooks/data-ingestion/useGetBatchList.ts
import useDataService from "@/services/useDataService";
import { GET_BATCH_LIST_URL } from "../../constants/urls";
import { usePageQuery } from "../useRouteQuery";
import { useDebugQuery } from "../useDebugQuery";
import { useAuthStore } from "../../store/authStore";

interface FileItem {
  file_id: number;
  file_name: string;
  file_format: string;
  upload_status: string;
  import_status: string;
  s3_path: string;
  s3_bucket: string;
}

export interface BatchItem {
  batch_id: number;
  batch_name: string;
  import_status: string;
  upload_status: string;
  total_file_count: number;
  notes: string;
  files: FileItem[];
}

export interface BatchListResponse {
  success: boolean;
  result: {
    data: {
      data: BatchItem[];
      total: number;
      current_page: number;
      page_size: number;
    };
  };
  message: string;
  code: number;
}

export const useGetBatchList = (
  page: number,
  size: number,
  sortOrder: string,
  searchFilter: string = "",
  storeFilter: string = "",
  statusFilter: string = ""
) => {
  const { user } = useAuthStore();
  const queryKey = ["batch-list", page, size, sortOrder, searchFilter, storeFilter, statusFilter];

  // Debug logging - remove this in production
  useDebugQuery('useGetBatchList', queryKey, true);

  return usePageQuery(
    queryKey,
    async () => {
      console.log(`🚀 Batch List API call triggered for user: ${user?.user_id} (${user?.email})`);
      let endpoint = `${GET_BATCH_LIST_URL}?page=${page}&size=${size}&sort_order=${sortOrder}`;

      // Add filters to the endpoint if they exist
      if (searchFilter) {
        endpoint += `&search=${encodeURIComponent(searchFilter)}`;
      }
      if (storeFilter) {
        endpoint += `&store=${encodeURIComponent(storeFilter)}`;
      }
      if (statusFilter) {
        endpoint += `&status=${encodeURIComponent(statusFilter)}`;
      }

      console.log(`📡 Batch List API endpoint: ${endpoint}`);
      return await useDataService.getService(endpoint);
    },
    {
      staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    }
  );
};
