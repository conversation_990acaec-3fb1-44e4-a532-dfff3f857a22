import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { useLocation } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { useAuthStore } from '../store/authStore';

/**
 * Custom hook that ensures queries trigger when landing on a route
 * This hook automatically enables the query when the component mounts
 * and provides better control over when API calls should happen
 *
 * IMPORTANT: This hook automatically includes user_id in query keys to prevent
 * cross-user data contamination when users switch without page refresh
 */
export function useRouteQuery<TData = unknown, TError = Error>(
  options: UseQueryOptions<TData, TError> & {
    queryKey: readonly unknown[];
    queryFn: () => Promise<TData>;
    refetchOnRouteChange?: boolean;
  }
) {
  const location = useLocation();
  const [isMounted, setIsMounted] = useState(false);
  const { user } = useAuthStore();

  // Create user-specific query key to prevent cross-user cache contamination
  const userSpecificQueryKey = user?.user_id
    ? [...options.queryKey, 'user', user.user_id]
    : options.queryKey;

  // Track when component mounts to ensure query runs
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Track route changes to potentially refetch data
  useEffect(() => {
    if (isMounted && options?.refetchOnRouteChange) {
      // Optional: refetch when route changes
    }
  }, [location.pathname, isMounted]);

  return useQuery<TData, TError>({
    ...options,
    queryKey: userSpecificQueryKey, // Use user-specific query key
    enabled: isMounted && (options.enabled !== false) && !!user?.user_id, // Only run if user is authenticated
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: false, // Disable refetch on window focus by default
    staleTime: options.staleTime ?? 5 * 60 * 1000, // Default 5 minutes cache
  });
}

/**
 * Hook specifically for page-level data fetching
 * Automatically triggers when landing on a route
 * Includes user_id in query key to prevent cross-user data contamination
 */
export function usePageQuery<TData = unknown, TError = Error>(
  queryKey: readonly unknown[],
  queryFn: () => Promise<TData>,
  options?: Partial<UseQueryOptions<TData, TError>>
) {
  return useRouteQuery({
    queryKey,
    queryFn,
    ...options,
  });
}

/**
 * Utility function to clear all user-specific cache when user logs out
 * Call this in your logout function to prevent data leakage
 */
export function clearUserCache(queryClient: any, userId: string) {
  // Remove all queries that contain this user's ID
  queryClient.removeQueries({
    predicate: (query: any) => {
      return query.queryKey.includes('user') && query.queryKey.includes(userId);
    }
  });

  console.log(`🧹 Cleared cache for user: ${userId}`);
}
