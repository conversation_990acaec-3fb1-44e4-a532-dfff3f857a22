import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { useLocation } from 'react-router-dom';
import { useEffect, useState } from 'react';

/**
 * Custom hook that ensures queries trigger when landing on a route
 * This hook automatically enables the query when the component mounts
 * and provides better control over when API calls should happen
 */
export function useRouteQuery<TData = unknown, TError = Error>(
  options: UseQueryOptions<TData, TError> & {
    queryKey: readonly unknown[];
    queryFn: () => Promise<TData>;
  }
) {
  const location = useLocation();
  const [isMounted, setIsMounted] = useState(false);

  // Track when component mounts to ensure query runs
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Track route changes to potentially refetch data
  useEffect(() => {
    if (isMounted && options.refetchOnRouteChange) {
      // Optional: refetch when route changes
    }
  }, [location.pathname, isMounted]);

  return useQuery<TData, TError>({
    ...options,
    enabled: isMounted && (options.enabled !== false), // Ensure query runs after mount
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: false, // Disable refetch on window focus by default
    staleTime: options.staleTime ?? 5 * 60 * 1000, // Default 5 minutes cache
  });
}

/**
 * Hook specifically for page-level data fetching
 * Automatically triggers when landing on a route
 */
export function usePageQuery<TData = unknown, TError = Error>(
  queryKey: readonly unknown[],
  queryFn: () => Promise<TData>,
  options?: Partial<UseQueryOptions<TData, TError>>
) {
  return useRouteQuery({
    queryKey,
    queryFn,
    ...options,
  });
}
