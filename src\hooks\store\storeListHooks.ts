import useDataService from "@/services/useDataService";
import { GET_STORELIST_URL } from "@/constants/urls";
import { usePageQuery } from "../useRouteQuery";

export interface Store {
  location_id: number;
  location_name: string;
  location_type_id: number;
  contact_name: string | null;
  contact_email: string | null;
  tenant_id: string;
  created_at: string;
}

interface GetStoreListResponse {
  success: boolean;
  message: string;
  code: number;
  result: {
    data: Store[];
    total_items: number;
    total_pages: number;
    current_page: number;
    page_size: number;
  };
}

export const useGetStoreList = (sortOrder: string = "desc", enabled: boolean = true) => {
  return usePageQuery<GetStoreListResponse>(
    ["store-list", sortOrder],
    async () => {
      const endpoint = `${GET_STORELIST_URL}?sort_order=${sortOrder}`;
      return await useDataService.getService(endpoint);
    },
    {
      enabled: enabled,
      staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    }
  );
};